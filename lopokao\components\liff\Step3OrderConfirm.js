/**
 * LIFF 訂購系統 - 步驟3：訂單確認
 */

function Step3OrderConfirm({ orderData, userProfile, onPrev, isLoading, setIsLoading, allProducts, productsLoaded }) {
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 使用傳入的商品資料，確保與 Step1 同步
    const products = allProducts || [];

    console.log('📋 Step3 接收商品資料:', {
        productsCount: products.length,
        productsLoaded: productsLoaded,
        orderDataProducts: orderData.products
    });
    
    // 獲取選購的商品 - 只包含有庫存的商品
    const getSelectedProducts = () => {
        if (!productsLoaded || products.length === 0) {
            return [];
        }

        return products.filter(product => {
            const qty = parseInt(orderData.products[product.id]) || 0;
            // 只包含有數量且有庫存的商品
            return qty > 0 && product.stock_status !== 'sold_out';
        }).map(product => {
            const quantity = parseInt(orderData.products[product.id]) || 0;
            return {
                ...product,
                quantity: quantity,
                subtotal: quantity * product.price
            };
        });
    };
    
    // 發送到 N8N webhook
    const sendToN8NWebhook = async (orderData) => {
        // 您需要在此處提供您的 N8N webhook URL
        const N8N_WEBHOOK_URL = 'https://cf.767780.xyz/webhook/line-order-menu-v4'; // 請替換為實際的 N8N webhook URL
        
        if (!N8N_WEBHOOK_URL || N8N_WEBHOOK_URL === 'YOUR_N8N_WEBHOOK_URL_HERE') {
            console.log('N8N webhook URL 未設置，跳過發送');
            return { success: true, message: 'N8N webhook 未設置' };
        }
        
        try {
            // 準備發送到 N8N 的資料格式 - 使用動態商品資料
            const selectedProducts = products.filter(product => {
                const qty = parseInt(orderData.products[product.id]) || 0;
                // 只包含有數量且有庫存的商品
                return qty > 0 && product.stock_status !== 'sold_out';
            }).map(product => {
                const quantity = parseInt(orderData.products[product.id]) || 0;
                return {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: quantity,
                    subtotal: quantity * product.price,
                    unit: product.unit || '條'
                };
            });

            console.log('📤 N8N 發送商品資料:', selectedProducts);
            
            // 取得 LINE 用戶資訊
            const lineUserInfo = {};
            if (userProfile) {
                lineUserInfo.userId = userProfile.userId;
                lineUserInfo.displayName = userProfile.displayName;
                lineUserInfo.pictureUrl = userProfile.pictureUrl;
                lineUserInfo.statusMessage = userProfile.statusMessage;
            }
            
            // 嘗試取得 replyToken（如果在 LIFF 環境中）
            let replyToken = null;
            if (typeof liff !== 'undefined' && liff.isInClient()) {
                try {
                    // 注意：replyToken 通常需要透過 webhook 事件取得
                    // 這裡只是為了示例，實際使用時可能需要其他方式
                    const context = liff.getContext();
                    if (context && context.type === 'utou') {
                        // 在一對一聊天中，可能需要其他方式取得 replyToken
                        console.log('在一對一聊天環境中');
                    }
                } catch (error) {
                    console.log('無法取得 LIFF context:', error);
                }
            }
            
            const webhookData = {
                timestamp: new Date().toISOString(),
                // LINE 用戶資訊
                lineUser: {
                    ...lineUserInfo,
                    replyToken: replyToken // 可能為 null
                },
                customer: {
                    name: orderData.customerName,
                    phone: orderData.phone,
                    contactMethod: orderData.contactMethod,
                    socialAccount: orderData.socialAccount
                },
                delivery: {
                    method: orderData.deliveryMethod,
                    address: orderData.deliveryMethod === '宅配到府' 
                        ? `${orderData.district} ${orderData.area} ${orderData.address}`
                        : `${orderData.storeName} 門市 (${orderData.storeAddress})`,
                    preferredDate: orderData.preferredDate,
                    preferredTime: orderData.preferredTime
                },
                order: {
                    products: selectedProducts,
                    shipping: orderData.shipping,
                    totalAmount: orderData.totalAmount,
                    paymentMethod: orderData.paymentMethod,
                    notes: orderData.notes
                },
                source: 'LIFF訂購系統'
            };
            
            const response = await fetch(N8N_WEBHOOK_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(webhookData)
            });
            
            if (response.ok) {
                console.log('✅ 成功發送到 N8N webhook');
                return { success: true, message: '成功發送到 N8N' };
            } else {
                throw new Error(`N8N webhook 回應錯誤: ${response.status}`);
            }
            
        } catch (error) {
            console.error('❌ 發送到 N8N webhook 失敗:', error);
            return { success: false, message: error.message };
        }
    };
    
    // 提交訂單
    const handleSubmitOrder = async () => {
        if (isSubmitting) return;
        
        setIsSubmitting(true);
        setIsLoading(true);
        
        try {
            // 準備包含完整商品資訊的訂單資料
            const enhancedOrderData = {
                ...orderData,
                selectedProducts: getSelectedProducts() // 包含完整商品資訊
            };

            console.log('📤 提交增強訂單資料:', enhancedOrderData);

            // 同時發送到 Google Sheets 和 N8N webhook
            const [sheetsResult, n8nResult] = await Promise.allSettled([
                window.submitOrderToSheets(enhancedOrderData),
                sendToN8NWebhook(orderData)
            ]);
            
            // 檢查 Google Sheets 結果
            const result = sheetsResult.status === 'fulfilled' ? sheetsResult.value : null;
            
            if (result && result.success) {
                // 檢查 N8N webhook 結果
                const n8nSuccess = n8nResult.status === 'fulfilled' && n8nResult.value.success;
                
                // 顯示成功訊息
                let successMessage = window.showDetailedResults(result);
                
                // 添加 N8N webhook 狀態到成功訊息
                if (n8nSuccess) {
                    successMessage += '\n\n✅ 已同步發送客戶提醒通知';
                } else if (n8nResult.status === 'fulfilled' && !n8nResult.value.success) {
                    successMessage += '\n\n⚠️ 訂單已儲存，但客戶通知發送失敗';
                    console.warn('N8N webhook 發送失敗:', n8nResult.value.message);
                }
                
                window.showSuccess(successMessage, '融氏蘿蔔糕', () => {
                    // 如果在 LIFF 環境中，關閉 LIFF 視窗
                    if (typeof liff !== 'undefined' && liff.isInClient()) {
                        try {
                            liff.closeWindow();
                        } catch (error) {
                            console.log('無法關閉 LIFF 視窗:', error);
                            // 如果無法關閉，顯示完成訊息
                            showCompletionMessage();
                        }
                    } else {
                        // 非 LIFF 環境，顯示完成畫面
                        showCompletionMessage();
                    }
                });
                
            } else {
                // 顯示錯誤訊息
                const errorDetails = result ? window.getErrorDetails(result) : '訂單提交過程發生錯誤';
                window.showError('訂單提交失敗\n\n' + errorDetails);
            }
            
        } catch (error) {
            console.error('訂單提交系統錯誤:', error);
            window.showError('系統發生未預期的錯誤，請稍後再試\n\n錯誤詳情：' + error.message);
        } finally {
            setIsSubmitting(false);
            setIsLoading(false);
        }
    };
    
    // 顯示完成訊息
    const showCompletionMessage = () => {
        const completionHtml = `
            <div style="text-align: center; padding: 40px 20px; background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                <div style="font-size: 64px; margin-bottom: 20px;">✅</div>
                <h2 style="color: #059669; font-size: 24px; margin-bottom: 16px;">訂單提交成功！</h2>
                <p style="color: #6b7280; margin-bottom: 24px;">感謝您的訂購，我們會盡快為您處理訂單。</p>
                <button onclick="window.location.reload()" style="background: #ef4444; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                    重新訂購
                </button>
            </div>
        `;
        document.body.innerHTML = completionHtml;
    };
    
    // 等待商品載入
    if (!productsLoaded || products.length === 0) {
        return (
            <div className="space-y-4">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">確認訂單</h2>
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
                        <p className="text-gray-600">載入商品資料中...</p>
                        <p className="text-xs text-gray-500 mt-2">
                            商品載入狀態: {productsLoaded ? '已載入' : '載入中'} |
                            商品數量: {products.length}
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    const selectedProducts = getSelectedProducts();

    console.log('📋 Step3 最終商品確認:', {
        allProducts: products.map(p => ({ id: p.id, name: p.name, stock_status: p.stock_status })),
        orderDataProducts: orderData.products,
        selectedProducts: selectedProducts.map(p => ({ id: p.id, name: p.name, quantity: p.quantity, subtotal: p.subtotal }))
    });

    return (
        <div className="space-y-4">
            {/* 標題 */}
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">確認訂單</h2>
                <p className="text-gray-600">請確認訂單內容無誤後提交</p>
            </div>
            
            <div className="scroll-area space-y-4">
                {/* 訂購商品 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">🛒</span>
                        訂購商品
                    </h3>
                    
                    <div className="space-y-3">
                        {selectedProducts.map(product => (
                            <div key={product.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <div className="flex-1">
                                    <div className="font-medium">{product.name}</div>
                                    <div className="text-sm text-gray-600">
                                        NT$ {product.price} × {product.quantity} {product.unit || '條'}
                                    </div>
                                </div>
                                <div className="font-bold text-gray-800">
                                    NT$ {product.subtotal}
                                </div>
                            </div>
                        ))}
                        
                        <div className="pt-2 space-y-2">
                            <div className="flex justify-between text-gray-600">
                                <span>商品小計</span>
                                <span>NT$ {orderData.totalAmount - orderData.shipping}</span>
                            </div>
                            <div className="flex justify-between text-gray-600">
                                <span>運費</span>
                                <span>
                                    {orderData.shipping === 0 ? '免運費' : `NT$ ${orderData.shipping}`}
                                </span>
                            </div>
                            <hr className="border-gray-200" />
                            <div className="flex justify-between text-lg font-bold text-red-600">
                                <span>總計</span>
                                <span>NT$ {orderData.totalAmount}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* 客戶資訊 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">👤</span>
                        客戶資訊
                    </h3>
                    
                    <div className="space-y-2 text-sm">
                        <div className="flex">
                            <span className="w-16 text-gray-600">姓名：</span>
                            <span className="font-medium">{orderData.customerName}</span>
                        </div>
                        <div className="flex">
                            <span className="w-16 text-gray-600">電話：</span>
                            <span className="font-medium">{orderData.phone}</span>
                        </div>
                        <div className="flex">
                            <span className="w-16 text-gray-600">聯繫：</span>
                            <span className="font-medium">{orderData.contactMethod}</span>
                        </div>
                        {orderData.socialAccount && (
                            <div className="flex">
                                <span className="w-16 text-gray-600">帳號：</span>
                                <span className="font-medium">{orderData.socialAccount}</span>
                            </div>
                        )}
                    </div>
                </div>
                
                {/* 配送資訊 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">🚚</span>
                        配送資訊
                    </h3>
                    
                    <div className="space-y-2 text-sm">
                        <div className="flex">
                            <span className="w-20 text-gray-600">配送方式：</span>
                            <span className="font-medium">{orderData.deliveryMethod}</span>
                        </div>
                        
                        {orderData.deliveryMethod === '宅配到府' ? (
                            <div className="flex">
                                <span className="w-20 text-gray-600">地址：</span>
                                <span className="font-medium flex-1">
                                    {orderData.district} {orderData.area} {orderData.address}
                                </span>
                            </div>
                        ) : (
                            <>
                                <div className="flex">
                                    <span className="w-20 text-gray-600">取貨門市：</span>
                                    <span className="font-medium">{orderData.storeName} 門市</span>
                                </div>
                                <div className="flex">
                                    <span className="w-20 text-gray-600">門市地址：</span>
                                    <span className="font-medium flex-1">{orderData.storeAddress}</span>
                                </div>
                                <div className="flex">
                                    <span className="w-20 text-gray-600">店號：</span>
                                    <span className="font-medium">{orderData.storeId}</span>
                                </div>
                            </>
                        )}
                        
                        <div className="flex">
                            <span className="w-20 text-gray-600">到貨日期：</span>
                            <span className="font-medium">{orderData.preferredDate}</span>
                        </div>
                        <div className="flex">
                            <span className="w-20 text-gray-600">到貨時間：</span>
                            <span className="font-medium">{orderData.preferredTime}</span>
                        </div>
                    </div>
                </div>
                
                {/* 付款方式 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">💳</span>
                        付款方式
                    </h3>
                    
                    <div className="text-sm">
                        <div className="font-medium mb-2">{orderData.paymentMethod}</div>
                        
                        {orderData.paymentMethod === '銀行轉帳' && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div className="text-blue-800 font-medium mb-1">轉帳資訊</div>
                                <div className="text-blue-700 text-xs space-y-1">
                                    <div>銀行：中國信託 (代碼：822)</div>
                                    <div>帳號：222540600078</div>
                                </div>
                            </div>
                        )}
                        
                        {orderData.notes && (
                            <div className="mt-3">
                                <div className="font-medium text-gray-700 mb-1">備註：</div>
                                <div className="text-gray-600 bg-gray-50 p-2 rounded">
                                    {orderData.notes}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                
                {/* 重要提醒 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-bold text-yellow-800 mb-2 flex items-center">
                        <span className="mr-2">💡</span>
                        重要提醒
                    </h4>
                    <div className="text-sm text-yellow-700 space-y-1">
                        <p>• 訂單提交後，請用電話或LINE、FB私訊客服確認訂單與出貨日期</p>
                        <p>• 如選擇銀行轉帳，請於轉帳後主動告知</p>
                        <p>• 如有任何問題，請隨時與我們聯繫</p>
                    </div>
                </div>
            </div>
            
            {/* 提交按鈕 */}
            <div className="flex space-x-3 pt-4">
                <button
                    onClick={onPrev}
                    className="primary-btn flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700"
                    disabled={isSubmitting}
                >
                    ← 返回修改
                </button>
                <button
                    onClick={handleSubmitOrder}
                    className={`primary-btn flex-1 ${
                        isSubmitting
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-red-500 hover:bg-red-600'
                    } text-white`}
                    disabled={isSubmitting}
                >
                    {isSubmitting ? (
                        <>
                            <div className="loading-spinner inline-block mr-2 w-4 h-4"></div>
                            提交中...
                        </>
                    ) : (
                        <>
                            確認提交訂單
                            <span className="ml-2">✓</span>
                        </>
                    )}
                </button>
            </div>
            
            {/* 提交中覆蓋層 */}
            {isSubmitting && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ margin: '-20px' }}>
                    <div className="bg-white rounded-lg p-6 max-w-xs mx-4 text-center">
                        <div className="loading-spinner mx-auto mb-4 w-8 h-8"></div>
                        <h3 className="font-bold text-gray-800 mb-2">處理中...</h3>
                        <p className="text-sm text-gray-600">正在提交您的訂單，請稍候</p>
                    </div>
                </div>
            )}
        </div>
    );
}

// 全域導出
window.Step3OrderConfirm = Step3OrderConfirm;