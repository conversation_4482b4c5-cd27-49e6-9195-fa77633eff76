<?php
/**
 * 統一的管理員 Session 檢查 API
 * 用於檢查 admin.php 的登入狀態
 */

session_start();

// 設定 CORS 標頭
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $action = $_GET['action'] ?? 'check';
    
    switch ($action) {
        case 'check':
            // 檢查 admin.php 的登入狀態
            $isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
            
            if ($isLoggedIn) {
                echo json_encode([
                    'success' => true,
                    'logged_in' => true,
                    'user' => [
                        'username' => 'admin',
                        'name' => '系統管理員',
                        'role' => 'admin',
                        'login_time' => $_SESSION['admin_login_time'] ?? date('Y-m-d H:i:s')
                    ],
                    'message' => '已登入'
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'logged_in' => false,
                    'message' => '未登入'
                ]);
            }
            break;
            
        case 'login':
            // 統一登入處理
            $input = json_decode(file_get_contents('php://input'), true);
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
            
            // 使用統一的密碼
            if ($username === 'admin' && $password === 'wade76167616') {
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_login_time'] = date('Y-m-d H:i:s');
                
                echo json_encode([
                    'success' => true,
                    'user' => [
                        'username' => 'admin',
                        'name' => '系統管理員',
                        'role' => 'admin',
                        'login_time' => $_SESSION['admin_login_time']
                    ],
                    'message' => '登入成功'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '帳號或密碼錯誤'
                ]);
            }
            break;
            
        case 'logout':
            // 登出處理
            $_SESSION['admin_logged_in'] = false;
            unset($_SESSION['admin_logged_in']);
            unset($_SESSION['admin_login_time']);
            
            echo json_encode([
                'success' => true,
                'message' => '已登出'
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '不支援的操作'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '系統錯誤: ' . $e->getMessage()
    ]);
}
?>
