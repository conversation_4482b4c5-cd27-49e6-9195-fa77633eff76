/**
 * 訂購說明組件
 * 展示訂購須知、配送資訊和保存方式
 */
function OrderInstructions() {
    try {
        // 訂購須知資料
        const orderRules = [
            {
                icon: "fas fa-clock",
                text: "請提前1-2天預訂，以確保新鮮製作"
            },
            {
                icon: "fas fa-gift",
                text: "訂購滿$350免運費"
            },
        ];

        // 付款方式資料
        const paymentInfo = [
            {
                title: "貨到付款",
                items: [
                /*    "宅配到府",
                    "7-11門市",
                    "來店自取"*/
                ]
            },
            {
                title: "銀行轉帳匯款",
                items: [
                /*    "中國信託銀行 (代碼：822)",
                    "帳號：222540600078",
                    "轉帳後請提供後五碼確認"*/
                ]
            }
        ];

        return (
            <section className="order-instructions-section py-16 bg-gray-50" data-name="order-instructions" id="order-info">
                <div className="container mx-auto px-4" data-name="instructions-container">
                    {/* 標題區塊 */}
                    <div className="text-center mb-12" data-name="instructions-header">
                        <h2 className="section-title text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                            配送資訊
                        </h2>
                    </div>

                    {/* 主要內容區塊 */}
                    <div className="bg-white rounded-lg shadow-lg p-8 max-w-6xl mx-auto" data-name="instructions-content">
                        {/* 配送資訊 */}
                        {/*<div className="delivery-info mt-8 pt-8 " data-name="delivery-info">*/}
                            <div className="grid md:grid-cols-3 gap-6">
                                <div className="text-center p-4 rounded-lg bg-green-50">
                                    <i className="fas fa-home text-green-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">宅配到府</h4>
                                    <p className="text-sm text-gray-600">專業配送，直達您家</p>
                                </div>
                                <div className="text-center p-4 rounded-lg bg-blue-50">
                                    <i className="fas fa-store text-blue-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">超商取貨</h4>
                                    <p className="text-sm text-gray-600">7-11便利取貨</p>
                                </div>
                                <div className="text-center p-4 rounded-lg bg-purple-50">
                                    <i className="fas fa-clock text-purple-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">來店自取</h4>
                                    <p className="text-sm text-gray-600">西螺鎮有2個自取點</p>
                                </div>
                            </div>
                        {/*</div>*/}
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('OrderInstructions component error:', error);
        reportError(error);
        return (
            <section className="order-instructions-section py-16 bg-gray-50">
                <div className="container mx-auto px-4 text-center">
                    <p className="text-gray-500">配送資訊載入中...</p>
                </div>
            </section>
        );
    }
}
