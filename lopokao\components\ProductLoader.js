/**
 * 動態商品載入器
 * 從後端 API 載入商品資料並動態生成商品選擇介面
 */

function ProductLoader() {
    const [products, setProducts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    // 載入商品資料
    React.useEffect(() => {
        loadProducts();
    }, []);

    const loadProducts = async () => {
        try {
            setLoading(true);
            setError(null);

            // 添加時間戳和防快取參數確保獲取即時資料
            const timestamp = new Date().getTime();
            const url = `./api/products_manager.php?path=frontend&t=${timestamp}&_=${Math.random()}`;

            const response = await fetch(url, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });
            const result = await response.json();
            
            if (result.success) {
                // 顯示所有啟用的商品（包含完售商品）
                const activeProducts = result.data.filter(product =>
                    product.is_active
                );

                console.log('✅ ProductLoader 載入商品:', activeProducts.length, '項商品');
                console.log('📋 商品單位詞檢查:', activeProducts.map(p => ({
                    id: p.id,
                    name: p.name,
                    unit: p.unit,
                    hasUnit: !!p.unit
                })));

                setProducts(activeProducts);
            } else {
                setError('載入商品失敗: ' + result.message);
            }
        } catch (err) {
            setError('載入商品時發生錯誤: ' + err.message);
            console.error('Product loading error:', err);
        } finally {
            setLoading(false);
        }
    };

    // 獲取商品狀態樣式
    const getProductStyle = (product) => {
        switch (product.stock_status) {
            case 'sold_out':
                return 'bg-gray-100 opacity-60';
            case 'limited':
                return 'border-orange-300 bg-orange-50';
            default:
                return '';
        }
    };

    // 獲取商品狀態文字
    const getStatusText = (product) => {
        switch (product.stock_status) {
            case 'sold_out':
                return '本期已完售';
            case 'limited':
                return '庫存有限';
            default:
                return '';
        }
    };

    // 獲取商品單位詞
    const getProductUnit = (product) => {
        return (product.unit && product.unit.trim() !== '') ? product.unit : '條';
    };

    // 渲染商品項目
    const renderProduct = (product, quantity, updateQuantity) => {
        const isDisabled = product.stock_status === 'sold_out';
        const productStyle = getProductStyle(product);
        const statusText = getStatusText(product);
        const unit = getProductUnit(product);

        return (
            <div key={product.id} className={`flex items-center justify-between p-3 border rounded-lg ${productStyle}`}>
                <div className="flex-1">
                    <span className={`font-medium ${isDisabled ? 'text-gray-500 line-through' : 'text-gray-700'}`}>
                        {product.name} (NT$ {product.price}/{unit})
                    </span>
                    {statusText && (
                        <div className={`text-base font-bold mt-2 px-2 py-1 rounded-md inline-block ${
                            product.stock_status === 'sold_out'
                                ? 'text-red-600 bg-red-100 border border-red-200'
                                : 'text-orange-600 bg-orange-100 border border-orange-200'
                        }`}>
                            {statusText}
                        </div>
                    )}
                    {product.stock_status === 'limited' && product.stock_quantity > 0 && (
                        <div className="text-xs text-orange-600 mt-2">
                            剩餘 {product.stock_quantity} {unit}
                        </div>
                    )}
                    {product.shipping_note && (
                        <div className="text-xs text-blue-600 mt-2">
                            💡 {product.shipping_note}
                        </div>
                    )}
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0">
                    <button
                        type="button"
                        onClick={() => updateQuantity(product.id, -1)}
                        disabled={isDisabled}
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold transition-colors ${
                            isDisabled 
                                ? 'bg-gray-300 cursor-not-allowed' 
                                : 'bg-gray-200 hover:bg-gray-300'
                        }`}
                    >
                        -
                    </button>
                    <span className={`w-6 text-center font-semibold text-sm ${isDisabled ? 'text-gray-400' : ''}`}>
                        {isDisabled ? 0 : quantity}
                    </span>
                    <button
                        type="button"
                        onClick={() => updateQuantity(product.id, 1)}
                        disabled={isDisabled || (product.stock_status === 'limited' && quantity >= product.stock_quantity)}
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold transition-colors ${
                            isDisabled || (product.stock_status === 'limited' && quantity >= product.stock_quantity)
                                ? 'bg-gray-300 cursor-not-allowed' 
                                : 'bg-red-500 hover:bg-red-600 text-white'
                        }`}
                    >
                        +
                    </button>
                </div>
            </div>
        );
    };

    return {
        products,
        loading,
        error,
        renderProduct,
        reload: loadProducts
    };
}

/**
 * 動態商品選擇組件
 * 使用 ProductLoader 載入商品並提供選擇介面
 */
function DynamicProductSelection({ formData, setFormData }) {
    const { products, loading, error, renderProduct, reload } = ProductLoader();

    // 當產品載入完成時，初始化 formData.products 以包含所有產品
    React.useEffect(() => {
        if (products.length > 0) {
            const newProducts = { ...formData.products };
            let hasChanges = false;
            const addedProducts = [];

            products.forEach(product => {
                if (!(product.id in newProducts)) {
                    newProducts[product.id] = 0;
                    hasChanges = true;
                    addedProducts.push(product.id);
                }
            });

            if (hasChanges) {
                console.log('🔄 初始化商品到 formData.products:', {
                    addedProducts: addedProducts,
                    beforeProducts: formData.products,
                    afterProducts: newProducts,
                    allLoadedProducts: products.map(p => ({ id: p.id, name: p.name }))
                });

                setFormData(prev => ({
                    ...prev,
                    products: newProducts
                }));
            }
        }
    }, [products, formData.products, setFormData]);

    // 更新商品數量
    const updateProductQuantity = (productId, change) => {
        const currentQty = parseInt(formData.products[productId]) || 0;
        const newQty = Math.max(0, currentQty + change);

        // 檢查商品狀態
        const product = products.find(p => p.id === productId);
        if (!product) {
            console.warn(`⚠️ 找不到商品 ID: ${productId}`);
            return;
        }

        // 完售商品不允許增加數量
        if (product.stock_status === 'sold_out') {
            if (change > 0) {
                alert(`❌ 商品 "${product.name}" 已完售，無法選購`);
                return;
            }
            // 允許減少數量（清零）
            setFormData(prev => ({
                ...prev,
                products: {
                    ...prev.products,
                    [productId]: 0
                }
            }));
            return;
        }

        // 檢查庫存限制
        if (product.stock_status === 'limited' && newQty > product.stock_quantity) {
            const unit = getProductUnit(product);
            alert(`⚠️ 庫存不足，最多只能選擇 ${product.stock_quantity} ${unit}`);
            return;
        }

        console.log(`📦 更新商品數量: ${product.name} ${currentQty} → ${newQty}`);

        setFormData(prev => ({
            ...prev,
            products: {
                ...prev.products,
                [productId]: newQty
            }
        }));
    };

    // 計算總金額 - 只計算有庫存的商品
    React.useEffect(() => {
        if (products.length > 0) {
            let subtotal = 0;
            let calculationDetails = [];

            products.forEach(product => {
                const quantity = parseInt(formData.products[product.id]) || 0;

                // 只計算有庫存或庫存有限的商品，排除完售商品
                if (product.stock_status !== 'sold_out' && quantity > 0) {
                    const itemTotal = quantity * product.price;
                    subtotal += itemTotal;
                    calculationDetails.push({
                        name: product.name,
                        quantity: quantity,
                        price: product.price,
                        total: itemTotal,
                        stock_status: product.stock_status
                    });
                } else if (product.stock_status === 'sold_out' && quantity > 0) {
                    // 警告：完售商品不計入金額
                    console.warn(`⚠️ 商品 "${product.name}" 已完售，不計入金額計算`);
                }
            });

            let shipping = 0;
            if (subtotal > 0 && subtotal < 350) {
                shipping = 100;
            }

            console.log('💰 金額計算詳情:', {
                calculationDetails,
                subtotal,
                shipping,
                totalAmount: subtotal + shipping
            });

            setFormData(prev => ({
                ...prev,
                shipping,
                totalAmount: subtotal + shipping
            }));
        }
    }, [formData.products, products]);

    if (loading) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                    <span className="ml-2 text-gray-600">載入商品中...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-red-800 font-medium">載入商品失敗</h3>
                            <p className="text-red-600 text-sm mt-1">{error}</p>
                        </div>
                        <button
                            onClick={reload}
                            className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                        >
                            重試
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (products.length === 0) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
                    <p className="text-gray-600">目前沒有可選擇的商品</p>
                    <button
                        onClick={reload}
                        className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                        重新載入
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4" data-name="product-selection">
            {products.map(product => 
                renderProduct(
                    product, 
                    formData.products[product.id] || 0, 
                    updateProductQuantity
                )
            )}
            
            {/* 重新載入按鈕 */}
            <div className="text-center pt-2">
                <button
                    type="button"
                    onClick={reload}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                >
                    🔄 重新載入商品
                </button>
            </div>
        </div>
    );
}

// 導出給其他組件使用
window.ProductLoader = ProductLoader;
window.DynamicProductSelection = DynamicProductSelection;
