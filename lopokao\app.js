function App() {
    try {
        const [admin, setAdmin] = React.useState(null);
        const [isLoading, setIsLoading] = React.useState(true);
        const [currentRoute, setCurrentRoute] = React.useState(window.location.hash);
        const [showDeliveryNotice, setShowDeliveryNotice] = React.useState(false);

        React.useEffect(() => {
            checkAdminSession().then(adminData => {
                setAdmin(adminData);
                setIsLoading(false);

                // 頁面載入完成後顯示配送提醒彈窗（僅在非管理後台頁面）
                if (!window.location.hash.includes('admin')) {
                    setTimeout(() => {
                        setShowDeliveryNotice(true);
                    }, 1000); // 延遲1秒顯示，讓頁面先完全載入
                }
            });

            // 監聽 hash 變化
            const handleHashChange = () => {
                setCurrentRoute(window.location.hash);
            };

            window.addEventListener('hashchange', handleHashChange);
            return () => window.removeEventListener('hashchange', handleHashChange);
        }, []);

        const handleLogin = async (adminData) => {
            await createAdminSession(adminData);
            setAdmin(adminData);
        };

        const handleLogout = async () => {
            await clearAdminSession();
            setAdmin(null);
            // 登出後回到首頁
            window.location.hash = '';
        };

        const handleCloseDeliveryNotice = () => {
            setShowDeliveryNotice(false);
        };

        if (isLoading) {
            return (
                <div className="flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
                        <div className="text-gray-600">載入中...</div>
                    </div>
                </div>
            );
        }

        // 檢查是否為管理後台路由
        if (currentRoute === '#admin' || currentRoute.startsWith('#admin')) {
            return admin ? (
                <AdminDashboard admin={admin} onLogout={handleLogout} />
            ) : (
                <Login onLogin={handleLogin} />
            );
        }

        return (
            <div className="app" data-name="app">
                <Header />
                <main data-name="main-content">
                    <Hero />
                    <BrandStory />                    
                    <ProcessSection />
                    <DynamicProducts />
                    <CustomerReviews />
                    <MediaSection />
                    <Storage />
                    <OrderInstructions />
                    <OrderGuide />
                </main>
                <Footer />
                <FloatingButtons />

                {/* 配送提醒彈窗 */}
                <DeliveryNoticeModal
                    isOpen={showDeliveryNotice}
                    onClose={handleCloseDeliveryNotice}
                />
            </div>
        );
    } catch (error) {
        console.error('App component error:', error);
        reportError(error);
        return null;
    }
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
