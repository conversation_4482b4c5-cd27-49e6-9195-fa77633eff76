/**
 * 後台管理系統統一配置
 * 統一管理 API 路徑和系統設定
 */

// API 基礎路徑配置
const ADMIN_CONFIG = {
    // API 路徑
    API: {
        BASE: '../api',
        PRODUCTS: '../api/products_manager.php',
        AUTH: '../api/auth.php',
        DELIVERY: '../api/delivery_settings.php',
        ORDERS: '../api/orders.php'
    },
    
    // 系統設定
    SYSTEM: {
        NAME: '融氏古早味蘿蔔糕 - 後台管理系統',
        VERSION: '2.0.0',
        TIMEOUT: 30000, // 30秒超時
        AUTO_REFRESH: 300000 // 5分鐘自動刷新
    },
    
    // 認證設定
    AUTH: {
        SESSION_KEY: 'admin_session',
        TOKEN_EXPIRY: 3600000, // 1小時
        AUTO_LOGOUT: 7200000 // 2小時無操作自動登出
    },
    
    // UI 設定
    UI: {
        ITEMS_PER_PAGE: 20,
        ANIMATION_DURATION: 300,
        NOTIFICATION_DURATION: 5000
    }
};

// 檢查當前環境
const getCurrentEnvironment = () => {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'local';
    } else if (hostname.includes('767780.xyz')) {
        return 'production';
    } else {
        return 'development';
    }
};

// 根據環境調整 API 路徑
const adjustApiPaths = () => {
    const env = getCurrentEnvironment();
    
    switch (env) {
        case 'local':
            // 本地開發環境
            break;
        case 'production':
            // 生產環境
            ADMIN_CONFIG.API.BASE = './api';
            ADMIN_CONFIG.API.PRODUCTS = './api/products_manager.php';
            ADMIN_CONFIG.API.AUTH = './api/auth.php';
            ADMIN_CONFIG.API.DELIVERY = './api/delivery_settings.php';
            ADMIN_CONFIG.API.ORDERS = './api/orders.php';
            break;
        default:
            // 開發環境
            break;
    }
};

// 初始化配置
adjustApiPaths();

// 導出配置（供其他檔案使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ADMIN_CONFIG;
}

// 全域變數（供舊版 JavaScript 使用）
window.ADMIN_CONFIG = ADMIN_CONFIG;

console.log('🔧 後台配置已載入:', {
    environment: getCurrentEnvironment(),
    apiBase: ADMIN_CONFIG.API.BASE,
    version: ADMIN_CONFIG.SYSTEM.VERSION
});
