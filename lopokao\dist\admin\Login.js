"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (c = i[4] || 3, u = i[5] === e ? i[3] : i[5], i[4] = 3, i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function Login(_ref) {
  var onLogin = _ref.onLogin;
  try {
    var _React$useState = React.useState({
        username: '',
        password: ''
      }),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      credentials = _React$useState2[0],
      setCredentials = _React$useState2[1];
    var _React$useState3 = React.useState(''),
      _React$useState4 = _slicedToArray(_React$useState3, 2),
      error = _React$useState4[0],
      setError = _React$useState4[1];
    var handleSubmit = /*#__PURE__*/function () {
      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(e) {
        var ADMIN_USERNAME, ADMIN_PASSWORD, adminData;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              e.preventDefault();
              try {
                // 固定的管理員帳密
                ADMIN_USERNAME = 'admin';
                ADMIN_PASSWORD = 'wade76167616';
                if (credentials.username === ADMIN_USERNAME && credentials.password === ADMIN_PASSWORD) {
                  adminData = {
                    objectId: 'admin_001',
                    objectData: {
                      username: ADMIN_USERNAME,
                      role: 'administrator',
                      loginTime: new Date().toISOString()
                    }
                  };
                  onLogin(adminData);
                } else {
                  setError('帳號或密碼錯誤');
                }
              } catch (error) {
                console.error('登入失敗：', error);
                setError('登入失敗，請稍後再試');
              }
            case 1:
              return _context.a(2);
          }
        }, _callee);
      }));
      return function handleSubmit(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    return /*#__PURE__*/React.createElement("div", {
      className: "login-container",
      "data-name": "login-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "login-card",
      "data-name": "login-card"
    }, /*#__PURE__*/React.createElement("h2", {
      className: "text-2xl font-bold mb-4 text-center",
      "data-name": "login-title"
    }, "\u5F8C\u53F0\u7BA1\u7406\u7CFB\u7D71"), /*#__PURE__*/React.createElement("div", {
      className: "bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6 text-sm"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex items-center mb-2"
    }, /*#__PURE__*/React.createElement("i", {
      className: "fas fa-info-circle text-blue-600 mr-2"
    }), /*#__PURE__*/React.createElement("span", {
      className: "font-semibold text-blue-800"
    }, "\u9810\u8A2D\u767B\u5165\u8CC7\u8A0A")), /*#__PURE__*/React.createElement("div", {
      className: "text-blue-600"
    }, /*#__PURE__*/React.createElement("div", null, "\u5E33\u865F\uFF1A", /*#__PURE__*/React.createElement("code", {
      className: "bg-blue-100 px-1 rounded"
    }, "admin")), /*#__PURE__*/React.createElement("div", null, "\u5BC6\u78BC\uFF1A", /*#__PURE__*/React.createElement("code", {
      className: "bg-blue-100 px-1 rounded"
    }, "admin123")))), error && /*#__PURE__*/React.createElement("div", {
      className: "bg-red-100 text-red-700 p-3 rounded mb-4",
      "data-name": "error-message"
    }, error), /*#__PURE__*/React.createElement("form", {
      onSubmit: handleSubmit,
      "data-name": "login-form"
    }, /*#__PURE__*/React.createElement("div", {
      className: "mb-4",
      "data-name": "form-group"
    }, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5E33\u865F"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      className: "w-full px-4 py-2 border rounded-md",
      value: credentials.username,
      onChange: function onChange(e) {
        return setCredentials(_objectSpread(_objectSpread({}, credentials), {}, {
          username: e.target.value
        }));
      },
      required: true
    })), /*#__PURE__*/React.createElement("div", {
      className: "mb-6",
      "data-name": "form-group"
    }, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5BC6\u78BC"), /*#__PURE__*/React.createElement("input", {
      type: "password",
      className: "w-full px-4 py-2 border rounded-md",
      value: credentials.password,
      onChange: function onChange(e) {
        return setCredentials(_objectSpread(_objectSpread({}, credentials), {}, {
          password: e.target.value
        }));
      },
      required: true
    })), /*#__PURE__*/React.createElement("button", {
      type: "submit",
      className: "w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700",
      "data-name": "submit-button"
    }, "\u767B\u5165"))));
  } catch (error) {
    console.error('Login component error:', error);
    reportError(error);
    return null;
  }
}