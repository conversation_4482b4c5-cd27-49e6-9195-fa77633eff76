/**
 * Google Sheets 儲存工具函數
 * 將訂單資料儲存到 Google Sheets
 *
 * @version 3.0
 * <AUTHOR>
 * @date 2024-12-20
 */

/**
 * 提交訂單到 Google Sheets
 * @param {Object} formData 表單資料
 * @returns {Promise<Object>} 處理結果
 */
const submitOrderToSheets = async (formData) => {
    try {
        // 準備 Google Sheets API 格式的資料
        const sheetsOrderData = await prepareSheetsData(formData);

        // 提交到 Google Sheets
        const result = await saveSheetsOrder(sheetsOrderData);

        return {
            success: true,
            message: '✅ 訂單已成功提交到 Google Sheets！',
            data: result,
            sms_sent: result.sms_sent || false,
            order_id: result.order_id || null
        };

    } catch (error) {
        console.error('Google Sheets 儲存失敗:', error);
        return {
            success: false,
            message: `❌ 訂單提交失敗: ${error.message}`,
            error: error
        };
    }
};

/**
 * 準備 Google Sheets API 格式的資料
 * @param {Object} formData 表單資料
 * @returns {Object} Google Sheets 格式的訂單資料
 */
// 格式化已選商品摘要（使用完整商品資訊）
const formatSelectedProductsSummary = (selectedProducts) => {
    const productSummary = selectedProducts.map(product => {
        return `${product.name} x ${product.quantity} ${product.unit || '條'} (NT$ ${product.subtotal})`;
    });

    console.log('📋 使用已選商品格式化摘要:', productSummary);
    return productSummary.join('; ');
};

// 格式化商品摘要（需要載入商品資訊）
const formatProductSummary = async (products) => {
    try {
        const timestamp = new Date().getTime();
        const url = `./api/products_manager.php?t=${timestamp}&_=${Math.random()}`;

        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        const result = await response.json();

        let productDetails = {};
        if (result.success) {
            result.data.forEach(product => {
                productDetails[product.id] = {
                    name: product.name,
                    price: product.price,
                    unit: product.unit || '條'
                };
            });
        } else {
            // 後備商品資訊
            productDetails = {
                'radish': { name: '原味蘿蔔糕', price: 250, unit: '條' },
                'taro': { name: '芋頭粿', price: 350, unit: '條' },
                'hongkong': { name: '台式鹹蘿蔔糕', price: 350, unit: '條' },
                'Test': { name: '鳳梨豆腐乳', price: 350, unit: '條' }
            };
        }

        const productSummary = [];
        Object.keys(products).forEach(productId => {
            const quantity = parseInt(products[productId]) || 0;
            if (quantity > 0) {
                const product = productDetails[productId];
                if (product) {
                    productSummary.push(`${product.name} x ${quantity} ${product.unit} (NT$ ${product.price * quantity})`);
                } else {
                    productSummary.push(`商品ID: ${productId} x ${quantity}`);
                }
            }
        });

        console.log('📋 格式化商品摘要:', productSummary);
        return productSummary.join('; ');

    } catch (error) {
        console.error('格式化商品摘要失敗:', error);
        return JSON.stringify(products);
    }
};

const prepareSheetsData = async (formData) => {
    // 處理地址資料
    let addressData = {};

    if (formData.deliveryMethod === '宅配到府') {
        // 宅配到府：傳送縣市、地區、地址
        addressData = {
            district: formData.district,
            area: formData.area,
            address: formData.address
        };
    } else if (formData.deliveryMethod === '7-11門市') {
        // 超商取貨：將門市資訊組合成地址格式
        const storeInfo = `${formData.storeName} 門市 (${formData.storeAddress}) [店號:${formData.storeId}]`;
        addressData = {
            address: storeInfo,
            storeName: formData.storeName,
            storeAddress: formData.storeAddress,
            storeId: formData.storeId
        };
    }

    return {
        customerName: formData.customerName,
        phone: formData.phone,
        deliveryMethod: formData.deliveryMethod,
        ...addressData, // 展開地址相關資料
        preferredDate: formData.preferredDate,
        preferredTime: formData.preferredTime,
        contactMethod: formData.contactMethod,
        socialAccount: formData.socialAccount,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        products: formData.products,
        productSummary: formData.selectedProducts
            ? formatSelectedProductsSummary(formData.selectedProducts)
            : await formatProductSummary(formData.products), // 可讀的商品摘要
        totalAmount: formData.totalAmount,
        shipping: formData.shipping,
        is_test_order: 'false' // 正式訂單
    };
};

/**
 * 儲存到 Google Sheets
 * @param {Object} orderData 訂單資料
 * @returns {Promise} 儲存結果
 */
const saveSheetsOrder = async (orderData) => {
    try {
        const response = await fetch('./api/sheets_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Google Sheets API 回傳錯誤');
        }

        return result;
    } catch (error) {
        console.error('Google Sheets 儲存錯誤:', error);
        throw new Error(`Google Sheets 儲存失敗: ${error.message}`);
    }
};

/**
 * 顯示詳細的提交結果
 * @param {Object} result 提交結果
 */
const showDetailedResults = (result) => {
    if (!result.success) {
        return result.message || '❌ 訂單提交失敗';
    }

    let message = '【融氏古早味蘿蔔糕】\n';
    message += '您的訂單已成功建立！\n\n';
    message += '請用電話或LINE,FB私訊客服確認訂單與出貨日期。\n\n';
    message += '感謝您的訂購！\n';
    message += '我們會盡快為您處理訂單。';

    return message;
};

/**
 * 格式化錯誤訊息
 * @param {Object} result 提交結果
 */
const getErrorDetails = (result) => {
    if (result.success) {
        return '';
    }

    return result.message || '未知錯誤';
};

// 匯出函數
window.submitOrderToSheets = submitOrderToSheets;
window.showDetailedResults = showDetailedResults;
window.getErrorDetails = getErrorDetails;
