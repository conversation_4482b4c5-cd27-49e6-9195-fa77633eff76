<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>庫存管理測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">庫存管理測試</h1>
        
        <!-- 測試區域 -->
        <div id="test-area"></div>
    </div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function StockManager() {
            const [products, setProducts] = useState([]);
            const [loading, setLoading] = useState(true);

            const loadProducts = async () => {
                try {
                    setLoading(true);
                    const response = await fetch('./api/products_manager.php');
                    const result = await response.json();
                    
                    if (result.success) {
                        setProducts(result.data);
                    }
                } catch (error) {
                    console.error('載入產品失敗:', error);
                } finally {
                    setLoading(false);
                }
            };

            const updateStock = async (productId, status, quantity) => {
                try {
                    const response = await fetch(`./api/products_manager.php?path=${productId}&action=stock`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            status: status,
                            quantity: quantity
                        })
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        alert('庫存更新成功！');
                        loadProducts(); // 重新載入產品列表
                    } else {
                        alert('更新失敗: ' + result.message);
                    }
                } catch (error) {
                    alert('更新時發生錯誤: ' + error.message);
                }
            };

            useEffect(() => {
                loadProducts();
            }, []);

            const getStatusColor = (status) => {
                switch (status) {
                    case 'available': return 'text-green-600 bg-green-100';
                    case 'limited': return 'text-orange-600 bg-orange-100';
                    case 'sold_out': return 'text-red-600 bg-red-100';
                    default: return 'text-gray-600 bg-gray-100';
                }
            };

            const getStatusText = (status) => {
                switch (status) {
                    case 'available': return '有庫存';
                    case 'limited': return '庫存有限';
                    case 'sold_out': return '已完售';
                    default: return '未知';
                }
            };

            if (loading) {
                return <div className="text-center">載入中...</div>;
            }

            return (
                <div className="space-y-6">
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">產品庫存管理</h2>
                        
                        <div className="space-y-4">
                            {products.map(product => (
                                <div key={product.id} className="border rounded-lg p-4">
                                    <div className="flex justify-between items-start mb-3">
                                        <div>
                                            <h3 className="text-lg font-bold">{product.name}</h3>
                                            <p className="text-gray-600">NT$ {product.price}</p>
                                        </div>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(product.stock_status)}`}>
                                            {getStatusText(product.stock_status)}
                                        </span>
                                    </div>
                                    
                                    <div className="mb-3">
                                        <p className="text-sm text-gray-600">
                                            庫存數量: {product.stock_quantity} {product.unit || '條'}
                                        </p>
                                    </div>
                                    
                                    <div className="flex space-x-2">
                                        <button
                                            onClick={() => updateStock(product.id, 'available', 100)}
                                            className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
                                        >
                                            設為有庫存 (100)
                                        </button>
                                        <button
                                            onClick={() => updateStock(product.id, 'limited', 10)}
                                            className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-sm"
                                        >
                                            設為庫存有限 (10)
                                        </button>
                                        <button
                                            onClick={() => updateStock(product.id, 'sold_out', 0)}
                                            className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm"
                                        >
                                            設為完售 (0)
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        <div className="mt-6 text-center">
                            <button
                                onClick={loadProducts}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                            >
                                重新載入產品
                            </button>
                        </div>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">測試連結</h2>
                        <div className="space-x-2">
                            <a 
                                href="./test_dynamic_order_form.html" 
                                target="_blank"
                                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded inline-block"
                            >
                                查看動態訂購表單
                            </a>
                            <a 
                                href="./index.html" 
                                target="_blank"
                                className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded inline-block"
                            >
                                查看主頁面
                            </a>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<StockManager />, document.getElementById('test-area'));
    </script>
</body>
</html>
