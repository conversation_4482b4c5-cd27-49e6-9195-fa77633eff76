# 後台管理系統架構說明

## 🏗️ 系統架構

### 統一後台入口
- **主入口**: `admin.php` - 統一的後台管理入口（包含登入檢查和 iframe 包裝）
- **登入頁面**: `admin-redirect.php` - 處理登入認證和重定向
- **管理介面**: `admin/secure_products.html` - 完整的產品管理系統

### 認證系統
- **API 認證**: `api/auth.php` - 統一的認證 API
- **密碼**: `wade76167616` (已更新，移除預設密碼提示)
- **Session 管理**: 使用 PHP Session 進行狀態管理

## 📂 檔案結構

```
lopokao/
├── admin.php                    # 統一後台入口（主要）
├── admin-redirect.php           # 登入頁面
├── admin/
│   ├── secure_products.html     # 完整產品管理系統（主要）
│   ├── secure_products.js       # 產品管理邏輯
│   ├── config.js               # 統一配置檔案
│   └── README.md               # 本說明文件
├── components/admin/            # React 組件（備用）
│   ├── Login.js                # React 登入組件
│   ├── AdminDashboard.js       # React 管理面板
│   └── ...                     # 其他 React 組件
└── api/
    ├── auth.php                # 認證 API
    ├── products_manager.php    # 產品管理 API
    └── ...                     # 其他 API
```

## 🔐 登入資訊

- **帳號**: `admin`
- **密碼**: `wade76167616`
- **登入方式**: 
  1. 訪問 `admin.php`
  2. 自動重定向到登入頁面
  3. 輸入帳號密碼
  4. 登入成功後進入管理系統

## 🎯 主要功能

### 產品管理
- 新增/編輯/刪除產品
- 庫存狀態管理（有庫存/庫存有限/已完售）
- 價格和描述管理
- 產品圖片管理

### 訂單管理
- 查看訂單列表
- 訂單狀態更新
- 客戶資訊管理

### 系統設定
- 到貨日設定
- 手抄單功能
- 系統重新載入

## 🔧 API 路徑

所有 API 路徑已統一在 `admin/config.js` 中管理：

```javascript
API: {
    BASE: '../api',
    PRODUCTS: '../api/products_manager.php',
    AUTH: '../api/auth.php',
    DELIVERY: '../api/delivery_settings.php',
    ORDERS: '../api/orders.php'
}
```

## 📱 響應式設計

系統支援桌面和行動裝置訪問：
- 桌面版：完整功能介面
- 行動版：優化的觸控介面
- 自動檢測裝置類型並調整顯示

## 🚀 使用方式

1. **訪問後台**: 直接訪問 `admin.php`
2. **登入系統**: 使用 `admin` / `wade76167616`
3. **管理產品**: 在產品管理頁面進行操作
4. **查看訂單**: 切換到訂單管理模式
5. **登出系統**: 點擊右上角登出按鈕

## ⚠️ 注意事項

- 密碼已更新為 `wade76167616`，不再顯示預設密碼提示
- 所有 API 路徑已統一，前後端串接正常
- 移除了重複的後台入口，統一使用 `admin/secure_products.html`
- React 組件保留作為備用，主要使用傳統 HTML/JS 版本
