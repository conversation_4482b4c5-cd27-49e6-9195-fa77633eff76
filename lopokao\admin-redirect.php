<?php
// 後台重定向頁面
// 檢查是否已登入（這裡可以加入你的登入驗證邏輯）

session_start();

// 處理登出請求
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    $_SESSION['admin_logged_in'] = false;
    unset($_SESSION['admin_logged_in']);
    session_destroy();
    header('Location: admin-redirect.php');
    exit;
}

// 簡單的登入檢查（你可以根據需要修改這個邏輯）
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

// 如果沒有登入，重定向到登入頁面
if (!$isLoggedIn) {
    // 檢查是否有登入請求
    if ($_POST && isset($_POST['username']) && isset($_POST['password'])) {
        // 這裡可以加入你的登入驗證邏輯
        // 暫時使用簡單的用戶名密碼驗證
        $username = $_POST['username'];
        $password = $_POST['password'];

        // 你可以修改這些登入憑證
        if ($username === 'admin' && $password === 'wade76167616') {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_login_time'] = date('Y-m-d H:i:s');
            // 登入成功，重定向到統一的後台入口
            header('Location: admin.php');
            exit;
        } else {
            $error = '用戶名或密碼錯誤';
        }
    }

    // 顯示登入表單
    ?>
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>後台管理系統 - 登入</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .login-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 400px;
            }

            .login-header {
                text-align: center;
                margin-bottom: 30px;
            }

            .login-header h1 {
                color: #333;
                font-size: 28px;
                margin-bottom: 10px;
            }

            .login-header p {
                color: #666;
                font-size: 14px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-group label {
                display: block;
                margin-bottom: 8px;
                color: #333;
                font-weight: 500;
            }

            .form-group input {
                width: 100%;
                padding: 12px 15px;
                border: 2px solid #e1e5e9;
                border-radius: 6px;
                font-size: 16px;
                transition: border-color 0.3s;
            }

            .form-group input:focus {
                outline: none;
                border-color: #667eea;
            }

            .login-btn {
                width: 100%;
                padding: 12px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: transform 0.2s;
            }

            .login-btn:hover {
                transform: translateY(-2px);
            }

            .error-message {
                background: #fee;
                color: #c33;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 20px;
                text-align: center;
                border: 1px solid #fcc;
            }

            .info-box {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
                margin-top: 20px;
            }

            .info-box h3 {
                color: #495057;
                font-size: 14px;
                margin-bottom: 10px;
            }

            .info-box p {
                color: #6c757d;
                font-size: 12px;
                line-height: 1.4;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="login-header">
                <h1>🔐 後台管理系統</h1>
                <p>融氏古早味蘿蔔糕 - 訂單管理後台</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="username">用戶名</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">密碼</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="login-btn">登入後台</button>
            </form>

            <div class="info-box">
                <h3>📋 系統說明</h3>
                <p>登入後將自動跳轉到訂單管理後台系統</p>
                <p>如果後台系統未啟動，請通知建育</p>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 如果已經登入，直接重定向到統一的後台入口
header('Location: admin.php');
exit;
?>
