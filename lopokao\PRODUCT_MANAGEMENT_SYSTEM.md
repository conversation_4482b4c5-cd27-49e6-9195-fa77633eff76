# 商品管理系統說明文件

## 📋 修改完成總結

### ✅ 台式鹹蘿蔔糕完售狀態修改
已成功將台式鹹蘿蔔糕設為不可選擇並顯示「本期已完售」狀態：

1. **視覺效果修改**：
   - 商品項目變為灰色背景，透明度降低
   - 商品名稱加上刪除線效果
   - 顯示紅色「本期已完售」標籤
   - 按鈕變為灰色且無法點擊

2. **功能邏輯修改**：
   - 計算總金額時不計算台式鹹蘿蔔糕
   - 驗證商品數量時不計算台式鹹蘿蔔糕
   - 訂單確認彈窗不顯示台式鹹蘿蔔糕

## 🏗️ 後端商品管理系統架構

### 📁 新增檔案結構
```
lopokao/
├── api/
│   ├── products_manager.php          # 商品管理 API
│   └── config/
│       └── products.json             # 商品資料檔案
├── admin/
│   ├── products.html                 # 商品管理介面
│   └── products.js                   # 商品管理 JavaScript
└── components/
    └── ProductLoader.js              # 動態商品載入器
```

### 🔧 API 端點說明

#### `api/products_manager.php`
完整的 RESTful API，支援以下操作：

**GET 請求**：
- `GET /api/products_manager.php` - 獲取所有商品
- `GET /api/products_manager.php?path=frontend` - 獲取前端格式商品
- `GET /api/products_manager.php?path={id}` - 獲取單一商品
- `GET /api/products_manager.php?include_inactive=true` - 包含停用商品

**POST 請求**：
- `POST /api/products_manager.php` - 新增商品

**PUT 請求**：
- `PUT /api/products_manager.php?path={id}` - 更新商品
- `PUT /api/products_manager.php?path={id}&action=stock` - 更新庫存

**DELETE 請求**：
- `DELETE /api/products_manager.php?path={id}` - 刪除商品（軟刪除）

### 📊 商品資料結構

```json
{
  "id": "radish",
  "name": "原味蘿蔔糕",
  "description": "使用新鮮蘿蔔與優質米漿精心製作...",
  "price": 250,
  "image": "/images/radish-cake.jpg",
  "ingredients": ["新鮮蘿蔔", "在來米漿", "香菇", "蔥", "素料"],
  "weight": "600g",
  "is_vegetarian": true,
  "stock_status": "available",     // available, limited, sold_out
  "stock_quantity": 100,
  "category": "traditional",       // traditional, premium, seasonal, general
  "sort_order": 1,
  "is_active": true,
  "created_at": "2025-01-28 10:00:00",
  "updated_at": "2025-01-28 10:00:00"
}
```

### 🎛️ 管理介面功能

#### `admin/products.html`
提供完整的商品管理介面：

1. **商品列表**：
   - 顯示所有商品的基本資訊
   - 支援排序、狀態篩選
   - 即時庫存狀態顯示

2. **商品編輯**：
   - 新增/編輯商品資訊
   - 上傳商品圖片
   - 設定成分、重量等詳細資訊

3. **庫存管理**：
   - 快速更新庫存狀態
   - 設定庫存數量
   - 批量操作支援

4. **狀態控制**：
   - 啟用/停用商品
   - 設定排序順序
   - 分類管理

### 🔄 動態商品載入

#### `components/ProductLoader.js`
提供前端動態載入商品的功能：

1. **自動載入**：
   - 從 API 自動載入最新商品資料
   - 支援錯誤處理和重試機制
   - 載入狀態顯示

2. **智能顯示**：
   - 根據庫存狀態調整顯示效果
   - 完售商品自動隱藏或標示
   - 庫存有限商品特殊提示

3. **即時計算**：
   - 根據選擇的商品自動計算總金額
   - 支援運費計算邏輯
   - 庫存限制檢查

## 🚀 使用方式

### 1. 管理員操作

#### 開啟商品管理介面
```
http://localhost/lopokao/admin/products.html
```

#### 基本操作流程
1. **新增商品**：點擊「新增商品」按鈕，填寫商品資訊
2. **編輯商品**：點擊商品列表中的「編輯」按鈕
3. **更新庫存**：點擊「庫存」按鈕快速更新庫存狀態
4. **啟用/停用**：點擊「啟用/停用」按鈕控制商品顯示

### 2. 前端整合

#### 替換靜態商品選擇
將 `OrderForm.js` 中的靜態商品選擇替換為動態載入：

```javascript
// 原本的靜態商品選擇
<div className="space-y-4" data-name="product-selection">
    {/* 靜態商品項目 */}
</div>

// 替換為動態商品選擇
<DynamicProductSelection 
    formData={formData} 
    setFormData={setFormData} 
/>
```

#### 載入 ProductLoader 組件
在 `index-liff.html` 中加入：

```html
<script type="text/babel" src="components/ProductLoader.js"></script>
```

### 3. API 測試

#### 測試商品 API
```bash
# 獲取所有商品
curl http://localhost/lopokao/api/products_manager.php

# 獲取前端格式商品
curl http://localhost/lopokao/api/products_manager.php?path=frontend

# 更新商品庫存
curl -X PUT http://localhost/lopokao/api/products_manager.php?path=hongkong&action=stock \
  -H "Content-Type: application/json" \
  -d '{"status":"sold_out","quantity":0}'
```

## 📈 系統優勢

### 1. 靈活性
- **動態商品管理**：無需修改程式碼即可新增/移除商品
- **即時庫存控制**：可即時調整商品可用性
- **多狀態支援**：支援有庫存、庫存有限、已完售等狀態

### 2. 易用性
- **視覺化管理**：直觀的管理介面
- **批量操作**：支援快速批量更新
- **即時預覽**：修改後立即在前端生效

### 3. 擴展性
- **RESTful API**：標準化的 API 設計
- **模組化架構**：易於擴展新功能
- **資料分離**：商品資料與程式邏輯分離

### 4. 維護性
- **JSON 資料格式**：易於備份和遷移
- **版本控制**：支援資料版本追蹤
- **錯誤處理**：完善的錯誤處理機制

## 🔮 未來擴展方向

### 1. 資料庫整合
- 將 JSON 檔案遷移到 MySQL 或 PostgreSQL
- 支援更複雜的查詢和關聯

### 2. 圖片管理
- 整合圖片上傳功能
- 支援多張商品圖片
- 圖片壓縮和優化

### 3. 庫存預警
- 低庫存自動提醒
- 銷售統計分析
- 自動補貨建議

### 4. 多語言支援
- 商品名稱和描述多語言
- 管理介面國際化
- 前端語言切換

## 📞 技術支援

如需協助或有問題，請檢查：
1. API 端點是否正常運作
2. 檔案權限是否正確設定
3. JSON 資料格式是否有效
4. 瀏覽器控制台是否有錯誤訊息

---

**建立日期**: 2025-01-28  
**版本**: v1.0  
**狀態**: ✅ 已完成並測試
