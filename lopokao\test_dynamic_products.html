<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>動態產品載入測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">動態產品載入測試</h1>
        
        <!-- 測試區域 -->
        <div id="test-area"></div>
    </div>

    <!-- 載入必要的工具函數 -->
    <script type="text/babel" src="utils/errorUtils.js"></script>
    
    <!-- 載入產品組件 -->
    <script type="text/babel" src="components/DynamicProducts.js"></script>
    <script type="text/babel" src="components/ProductLoader.js"></script>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function TestApp() {
            const [formData, setFormData] = useState({
                products: {
                    radish: 0,
                    taro: 0,
                    hongkong: 0
                },
                shipping: 0,
                totalAmount: 0
            });

            // 計算總價
            useEffect(() => {
                calculateTotal();
            }, [formData.products]);

            const calculateTotal = () => {
                let subtotal = 0;
                
                // 產品價格對應表
                const productPrices = {
                    'radish': 250,
                    'taro': 350,
                    'hongkong': 350
                };
                
                // 計算所有產品的小計
                Object.keys(formData.products).forEach(productId => {
                    const quantity = parseInt(formData.products[productId]) || 0;
                    const price = productPrices[productId] || 0;
                    subtotal += quantity * price;
                });

                let shipping = 0;
                if (subtotal > 0 && subtotal < 350) {
                    shipping = 100;
                }

                setFormData(prev => ({
                    ...prev,
                    shipping,
                    totalAmount: subtotal + shipping
                }));
            };

            return (
                <div className="space-y-8">
                    {/* 動態產品展示 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">動態產品展示</h2>
                        <DynamicProducts />
                    </div>

                    {/* 動態產品選擇 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">動態產品選擇</h2>
                        <DynamicProductSelection 
                            formData={formData} 
                            setFormData={setFormData} 
                        />
                        
                        {/* 訂單摘要 */}
                        <div className="mt-6 bg-gray-50 p-4 rounded-lg">
                            <h3 className="font-bold mb-2">訂單摘要</h3>
                            <div className="grid grid-cols-2 gap-2">
                                <p>商品小計：</p>
                                <p className="text-right">NT$ {formData.totalAmount - formData.shipping}</p>
                                <p>運費：</p>
                                <p className="text-right">NT$ {formData.shipping}</p>
                                <p className="font-bold">總計：</p>
                                <p className="text-right font-bold text-red-600">NT$ {formData.totalAmount}</p>
                            </div>
                        </div>
                    </div>

                    {/* API 測試 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">API 測試</h2>
                        <div className="space-y-2">
                            <button 
                                onClick={() => window.open('./api/products_manager.php', '_blank')}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-2"
                            >
                                測試完整 API
                            </button>
                            <button 
                                onClick={() => window.open('./api/products_manager.php?path=frontend', '_blank')}
                                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                            >
                                測試前端 API
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('test-area'));
    </script>
</body>
</html>
