<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>動態訂購表單測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/order.css" rel="stylesheet">
    <link href="styles/products.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">動態訂購表單測試</h1>
        
        <!-- 測試區域 -->
        <div id="test-area"></div>
    </div>

    <!-- 載入必要的工具函數 -->
    <script type="text/babel" src="utils/errorUtils.js"></script>
    <script type="text/babel" src="utils/cityDistricts.js"></script>
    <script type="text/babel" src="utils/orderUtils.js"></script>
    <script type="text/babel" src="utils/sheetsUtils.js"></script>
    
    <!-- 載入產品組件 -->
    <script type="text/babel" src="components/DynamicProducts.js"></script>
    <script type="text/babel" src="components/ProductLoader.js"></script>
    <script type="text/babel" src="components/OrderForm.js"></script>

    <script type="text/babel">
        function TestApp() {
            return (
                <div className="space-y-8">
                    {/* 動態產品展示 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">我們的商品（動態載入）</h2>
                        <DynamicProducts />
                    </div>

                    {/* 動態訂購表單 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-6 text-center">訂購表單（動態載入）</h2>
                        <OrderForm />
                    </div>

                    {/* API 測試區域 */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-2xl font-bold mb-4">API 測試</h2>
                        <div className="space-y-2">
                            <button 
                                onClick={() => window.open('./api/products_manager.php', '_blank')}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-2"
                            >
                                查看完整產品 API
                            </button>
                            <button 
                                onClick={() => window.open('./api/products_manager.php?path=frontend', '_blank')}
                                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded mr-2"
                            >
                                查看前端產品 API
                            </button>
                            <button 
                                onClick={() => window.open('./api/config/products.json', '_blank')}
                                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded"
                            >
                                查看產品配置檔案
                            </button>
                        </div>
                        
                        <div className="mt-4 p-4 bg-gray-50 rounded">
                            <h3 className="font-bold mb-2">測試說明：</h3>
                            <ul className="text-sm space-y-1">
                                <li>• 產品展示和訂購表單都從 API 動態載入</li>
                                <li>• 完售商品會顯示但不可選擇</li>
                                <li>• 庫存有限商品會顯示剩餘數量</li>
                                <li>• 價格和庫存狀態即時同步</li>
                                <li>• 移除了重新整理按鈕</li>
                            </ul>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('test-area'));
    </script>
</body>
</html>
