function Login({ onLogin }) {
    try {
        const [credentials, setCredentials] = React.useState({
            username: '',
            password: ''
        });
        const [error, setError] = React.useState('');

        const handleSubmit = async (e) => {
            e.preventDefault();
            try {
                // 固定的管理員帳密
                const ADMIN_USERNAME = 'admin';
                const ADMIN_PASSWORD = 'wade76167616';

                if (credentials.username === ADMIN_USERNAME && credentials.password === ADMIN_PASSWORD) {
                    const adminData = {
                        objectId: 'admin_001',
                        objectData: {
                            username: ADMIN_USERNAME,
                            role: 'administrator',
                            loginTime: new Date().toISOString()
                        }
                    };
                    onLogin(adminData);
                } else {
                    setError('帳號或密碼錯誤');
                }
            } catch (error) {
                console.error('登入失敗：', error);
                setError('登入失敗，請稍後再試');
            }
        };

        return (
            <div className="login-container" data-name="login-container">
                <div className="login-card" data-name="login-card">
                    <h2 className="text-2xl font-bold mb-4 text-center" data-name="login-title">
                        後台管理系統
                    </h2>

                    {error && (
                        <div className="bg-red-100 text-red-700 p-3 rounded mb-4" data-name="error-message">
                            {error}
                        </div>
                    )}
                    <form onSubmit={handleSubmit} data-name="login-form">
                        <div className="mb-4" data-name="form-group">
                            <label className="block text-gray-700 mb-2">帳號</label>
                            <input
                                type="text"
                                className="w-full px-4 py-2 border rounded-md"
                                value={credentials.username}
                                onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                                required
                            />
                        </div>
                        <div className="mb-6" data-name="form-group">
                            <label className="block text-gray-700 mb-2">密碼</label>
                            <input
                                type="password"
                                className="w-full px-4 py-2 border rounded-md"
                                value={credentials.password}
                                onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                                required
                            />
                        </div>
                        <button
                            type="submit"
                            className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700"
                            data-name="submit-button"
                        >
                            登入
                        </button>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Login component error:', error);
        reportError(error);
        return null;
    }
}
