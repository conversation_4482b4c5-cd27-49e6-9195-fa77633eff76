<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快取修正測試 - 融氏古早味蘿蔔糕</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-result {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
        }
        .test-success {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .test-error {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        .test-warning {
            border-color: #f59e0b;
            background-color: #fffbeb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">🧪 快取修正測試</h1>
        
        <div class="max-w-4xl mx-auto">
            <!-- 測試控制面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold mb-4">測試控制面板</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testApiCache()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        🔄 測試 API 快取
                    </button>
                    <button onclick="testMultipleRequests()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        📡 多次請求測試
                    </button>
                    <button onclick="clearResults()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                        🗑️ 清除結果
                    </button>
                </div>
            </div>

            <!-- 測試結果顯示區域 -->
            <div id="testResults" class="space-y-4">
                <!-- 測試結果將在這裡顯示 -->
            </div>

            <!-- 即時監控區域 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h2 class="text-xl font-bold mb-4">📊 即時監控</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="font-semibold mb-2">請求統計</h3>
                        <div id="requestStats" class="text-sm text-gray-600">
                            <div>總請求數: <span id="totalRequests">0</span></div>
                            <div>成功請求: <span id="successRequests">0</span></div>
                            <div>失敗請求: <span id="failedRequests">0</span></div>
                            <div>平均回應時間: <span id="avgResponseTime">0ms</span></div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-2">快取狀態</h3>
                        <div id="cacheStatus" class="text-sm text-gray-600">
                            <div>瀏覽器快取: <span id="browserCache">檢測中...</span></div>
                            <div>API 標頭: <span id="apiHeaders">檢測中...</span></div>
                            <div>時間戳參數: <span id="timestampParam">檢測中...</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API 回應詳情 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <h2 class="text-xl font-bold mb-4">🔍 最新 API 回應詳情</h2>
                <pre id="apiResponse" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
等待 API 測試...
                </pre>
            </div>
        </div>
    </div>

    <script>
        let requestCount = 0;
        let successCount = 0;
        let failedCount = 0;
        let responseTimes = [];

        // 測試 API 快取
        async function testApiCache() {
            const startTime = Date.now();
            addTestResult('🔄 開始測試 API 快取...', 'info');

            try {
                // 添加時間戳和防快取參數
                const timestamp = new Date().getTime();
                const url = `./api/products_manager.php?t=${timestamp}&_=${Math.random()}`;
                
                console.log('🔗 測試 URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    cache: 'no-cache',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                const responseTime = Date.now() - startTime;
                requestCount++;
                responseTimes.push(responseTime);

                // 檢查回應標頭
                const cacheControl = response.headers.get('Cache-Control');
                const pragma = response.headers.get('Pragma');
                const expires = response.headers.get('Expires');

                if (response.ok) {
                    successCount++;
                    const result = await response.json();
                    
                    addTestResult(`✅ API 請求成功 (${responseTime}ms)`, 'success');
                    addTestResult(`📊 載入 ${result.data?.length || 0} 項商品`, 'info');
                    
                    // 檢查防快取標頭
                    if (cacheControl && cacheControl.includes('no-cache')) {
                        addTestResult('✅ 防快取標頭正確設定', 'success');
                    } else {
                        addTestResult('⚠️ 防快取標頭可能未正確設定', 'warning');
                    }

                    // 顯示 API 回應
                    document.getElementById('apiResponse').textContent = JSON.stringify(result, null, 2);
                    
                } else {
                    failedCount++;
                    addTestResult(`❌ API 請求失敗: ${response.status} ${response.statusText}`, 'error');
                }

                updateStats();
                updateCacheStatus(cacheControl, pragma, expires);

            } catch (error) {
                failedCount++;
                requestCount++;
                addTestResult(`❌ 請求錯誤: ${error.message}`, 'error');
                updateStats();
            }
        }

        // 多次請求測試
        async function testMultipleRequests() {
            addTestResult('📡 開始多次請求測試 (5次)...', 'info');
            
            for (let i = 1; i <= 5; i++) {
                addTestResult(`🔄 第 ${i} 次請求...`, 'info');
                await testApiCache();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
            
            addTestResult('✅ 多次請求測試完成', 'success');
        }

        // 添加測試結果
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `
                <div class="flex justify-between items-center">
                    <span>${message}</span>
                    <span class="text-sm text-gray-500">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            resultsDiv.appendChild(resultDiv);
            
            // 自動滾動到最新結果
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 更新統計資訊
        function updateStats() {
            document.getElementById('totalRequests').textContent = requestCount;
            document.getElementById('successRequests').textContent = successCount;
            document.getElementById('failedRequests').textContent = failedCount;
            
            if (responseTimes.length > 0) {
                const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                document.getElementById('avgResponseTime').textContent = Math.round(avgTime) + 'ms';
            }
        }

        // 更新快取狀態
        function updateCacheStatus(cacheControl, pragma, expires) {
            document.getElementById('browserCache').textContent = 
                cacheControl && cacheControl.includes('no-cache') ? '✅ 已停用' : '❌ 可能啟用';
            
            document.getElementById('apiHeaders').textContent = 
                cacheControl ? '✅ 已設定' : '❌ 未設定';
            
            document.getElementById('timestampParam').textContent = '✅ 已添加';
        }

        // 清除結果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('apiResponse').textContent = '等待 API 測試...';
            requestCount = 0;
            successCount = 0;
            failedCount = 0;
            responseTimes = [];
            updateStats();
        }

        // 頁面載入時自動執行一次測試
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('🚀 頁面載入完成，開始自動測試...', 'info');
                testApiCache();
            }, 1000);
        });
    </script>
</body>
</html>
